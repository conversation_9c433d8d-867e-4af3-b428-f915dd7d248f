{"rustc": 16591470773350601817, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 15599109589607159429, "path": 15102792655553339975, "deps": [[4925398738524877221, "clap_derive", false, 5003849101250689888], [14814905555676593471, "clap_builder", false, 17313076794841473979]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-feddfe05cc89b062\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}