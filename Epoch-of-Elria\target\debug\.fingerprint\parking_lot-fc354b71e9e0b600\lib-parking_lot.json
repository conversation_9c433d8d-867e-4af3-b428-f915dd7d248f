{"rustc": 16591470773350601817, "features": "[\"default\"]", "declared_features": "[\"arc_lock\", \"deadlock_detection\", \"default\", \"nightly\", \"owning_ref\", \"send_guard\", \"serde\", \"stdweb\", \"wasm-bindgen\"]", "target": 14160162848842265298, "profile": 2241668132362809309, "path": 266872020754722672, "deps": [[8081351675046095464, "lock_api", false, 14099127129445371096], [14196108479452351812, "instant", false, 5033482720801145439], [14814334185036658946, "parking_lot_core", false, 6619996361013338778]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\parking_lot-fc354b71e9e0b600\\dep-lib-parking_lot", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}