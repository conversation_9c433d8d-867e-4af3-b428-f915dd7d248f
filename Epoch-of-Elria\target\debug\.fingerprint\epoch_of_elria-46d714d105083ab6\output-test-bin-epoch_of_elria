{"$message_type":"diagnostic","message":"encountered diff marker","code":null,"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":0,"byte_end":7,"line_start":1,"line_end":1,"column_start":1,"column_end":8,"is_primary":true,"text":[{"text":"<<<<<<< HEAD","highlight_start":1,"highlight_end":8}],"label":"between this marker and `=======` is the code that we're merging into","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":10873,"byte_end":10880,"line_start":226,"line_end":226,"column_start":1,"column_end":8,"is_primary":false,"text":[{"text":"=======","highlight_start":1,"highlight_end":8}],"label":"between this marker and `>>>>>>>` is the incoming code","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":26590,"byte_end":26597,"line_start":566,"line_end":566,"column_start":1,"column_end":8,"is_primary":true,"text":[{"text":">>>>>>> 42d16f86361b2d5498becedc5ef898ef6702cb47","highlight_start":1,"highlight_end":8}],"label":"this marker concludes the conflict region","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"conflict markers indicate that a merge was started but could not be completed due to merge conflicts\nto resolve a conflict, keep only the code you want and then delete the lines containing conflict markers","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if you're having merge conflicts after pulling new code:\nthe top section is the code you already had and the bottom section is the remote code\nif you're in the middle of a rebase:\nthe top section is the code being rebased onto and the bottom section is the code coming from the current commit being rebased","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for an explanation on these markers from the `git` documentation:\nvisit <https://git-scm.com/book/en/v2/Git-Tools-Advanced-Merging#_checking_out_conflicts>","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: encountered diff marker\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:1:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m<<<<<<< HEAD\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mbetween this marker and `=======` is the code that we're merging into\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m226\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m=======\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbetween this marker and `>>>>>>>` is the incoming code\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m566\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m>>>>>>> 42d16f86361b2d5498becedc5ef898ef6702cb47\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthis marker concludes the conflict region\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: conflict markers indicate that a merge was started but could not be completed due to merge conflicts\u001b[0m\n\u001b[0m            to resolve a conflict, keep only the code you want and then delete the lines containing conflict markers\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: if you're having merge conflicts after pulling new code:\u001b[0m\n\u001b[0m            the top section is the code you already had and the bottom section is the remote code\u001b[0m\n\u001b[0m            if you're in the middle of a rebase:\u001b[0m\n\u001b[0m            the top section is the code being rebased onto and the bottom section is the code coming from the current commit being rebased\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for an explanation on these markers from the `git` documentation:\u001b[0m\n\u001b[0m            visit <https://git-scm.com/book/en/v2/Git-Tools-Advanced-Merging#_checking_out_conflicts>\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error\u001b[0m\n\n"}
