{"rustc": 15597765236515928571, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 9656904095642909417, "path": 6619605074826535770, "deps": [[5820056977320921005, "anstream", false, 2101284895322680894], [9394696648929125047, "anstyle", false, 7580611421514951040], [11166530783118767604, "strsim", false, 12483397967236397660], [11649982696571033535, "clap_lex", false, 4352113289884561732]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_builder-5004576240be5b27/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}