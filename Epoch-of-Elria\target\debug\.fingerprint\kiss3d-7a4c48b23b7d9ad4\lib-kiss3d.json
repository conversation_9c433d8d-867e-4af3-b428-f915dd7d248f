{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"conrod\", \"conrod_core\"]", "target": 14077391568679265771, "profile": 2241668132362809309, "path": 12975302710045843475, "deps": [[98782964599244774, "nalgebra", false, 13605899702839794921], [2924422107542798392, "libc", false, 17147004237193511033], [5157631553186200874, "num_traits", false, 46062835796420640], [5193790429087308582, "rusttype", false, 16223485309197604973], [6287287159198654388, "glutin", false, 12186858173221122713], [6322834029183992206, "glow", false, 7542675994724517449], [6925051261804233560, "image", false, 10640183181119485378], [9689903380558560274, "serde", false, 16983437651910000113], [10435729446543529114, "bitflags", false, 13806316718450859304], [12170264697963848012, "either", false, 5837731613501307833], [13775881390216113583, "ncollide3d", false, 827685689258610030], [14196108479452351812, "instant", false, 5033482720801145439], [16257276029081467297, "serde_derive", false, 5934179835934583777]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\kiss3d-7a4c48b23b7d9ad4\\dep-lib-kiss3d", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}