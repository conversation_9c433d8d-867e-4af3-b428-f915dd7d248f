{"rustc": 16591470773350601817, "features": "[\"mio\", \"mio-extras\", \"parking_lot\", \"percent-encoding\", \"sctk\", \"wayland\", \"wayland-client\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"mio\", \"mio-extras\", \"parking_lot\", \"percent-encoding\", \"sctk\", \"serde\", \"std_web\", \"stdweb\", \"wasm-bindgen\", \"wayland\", \"wayland-client\", \"web-sys\", \"web_sys\", \"x11\", \"x11-dl\"]", "target": 5956907280325987529, "profile": 2241668132362809309, "path": 13249647836956071967, "deps": [[2481439302769428604, "raw_window_handle", false, 14052910957457401605], [2924422107542798392, "libc", false, 17147004237193511033], [5986029879202738730, "log", false, 17293751965357291924], [10020888071089587331, "<PERSON>ap<PERSON>", false, 11911911858306910069], [10435729446543529114, "bitflags", false, 13806316718450859304], [11641406201058336332, "parking_lot", false, 17704089081895887541], [14196108479452351812, "instant", false, 5033482720801145439], [17917672826516349275, "lazy_static", false, 9776750328437871497]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\winit-197f8360dea58f5c\\dep-lib-winit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}