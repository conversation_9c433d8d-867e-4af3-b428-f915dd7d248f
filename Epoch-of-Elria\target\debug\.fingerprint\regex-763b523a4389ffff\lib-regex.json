{"rustc": 16591470773350601817, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2241668132362809309, "path": 8951051242614557546, "deps": [[555019317135488525, "regex_automata", false, 9388531202043265684], [2779309023524819297, "aho_corasick", false, 9800710240881351263], [3129130049864710036, "memchr", false, 17169273536976230750], [9408802513701742484, "regex_syntax", false, 16767106298028423109]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-763b523a4389ffff\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}