{"rustc": 16591470773350601817, "features": "[\"default\", \"macros\", \"matrixmultiply\", \"nalgebra-macros\", \"std\"]", "declared_features": "[\"abomonation\", \"abomonation-serialize\", \"alga\", \"alloc\", \"arbitrary\", \"bytemuck\", \"compare\", \"convert-bytemuck\", \"convert-glam013\", \"convert-glam014\", \"convert-glam015\", \"convert-glam016\", \"convert-glam017\", \"convert-glam018\", \"convert-glam019\", \"convert-glam020\", \"convert-mint\", \"cuda\", \"cust\", \"debug\", \"default\", \"glam013\", \"glam014\", \"glam015\", \"glam016\", \"glam017\", \"glam018\", \"glam019\", \"glam020\", \"io\", \"libm\", \"libm-force\", \"macros\", \"matrixcompare-core\", \"matrixmultiply\", \"mint\", \"nalgebra-macros\", \"pest\", \"pest_derive\", \"proptest\", \"proptest-support\", \"quickcheck\", \"rand\", \"rand-no-std\", \"rand-package\", \"rand_distr\", \"rkyv\", \"rkyv-serialize\", \"rkyv-serialize-no-std\", \"serde\", \"serde-serialize\", \"serde-serialize-no-std\", \"slow-tests\", \"sparse\", \"std\"]", "target": 572955357253318494, "profile": 2241668132362809309, "path": 780507431328662709, "deps": [[2819946551904607991, "num_rational", false, 13442851824087068551], [5157631553186200874, "num_traits", false, 46062835796420640], [12319020793864570031, "num_complex", false, 4953970168904278541], [15677050387741058262, "approx", false, 10779454468097401228], [15826188163127377936, "matrixmultiply", false, 11527152237629480109], [17001665395952474378, "typenum", false, 11434323370087321513], [17514367198935401919, "nalgebra_macros", false, 13362347580580075709], [17877237321909315803, "simba", false, 12514262026128991647]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\nalgebra-fa89db13158c0009\\dep-lib-nalgebra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}