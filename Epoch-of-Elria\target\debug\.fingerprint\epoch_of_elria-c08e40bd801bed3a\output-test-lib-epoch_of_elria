{"$message_type":"diagnostic","message":"unused imports: `Collectible`, `Enemy`, `Platform`, and `Player`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_framework.rs","byte_start":125,"byte_end":131,"line_start":3,"line_end":3,"column_start":39,"column_end":45,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":39,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\game_framework.rs","byte_start":133,"byte_end":144,"line_start":3,"line_end":3,"column_start":47,"column_end":58,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":47,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\game_framework.rs","byte_start":146,"byte_end":151,"line_start":3,"line_end":3,"column_start":60,"column_end":65,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":60,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\game_framework.rs","byte_start":153,"byte_end":161,"line_start":3,"line_end":3,"column_start":67,"column_end":75,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":67,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\game_framework.rs","byte_start":123,"byte_end":161,"line_start":3,"line_end":3,"column_start":37,"column_end":75,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":37,"highlight_end":75}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\game_framework.rs","byte_start":112,"byte_end":113,"line_start":3,"line_end":3,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\game_framework.rs","byte_start":161,"byte_end":162,"line_start":3,"line_end":3,"column_start":75,"column_end":76,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":75,"highlight_end":76}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Collectible`, `Enemy`, `Platform`, and `Player`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_framework.rs:3:39\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::scene::Scene`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_framework.rs","byte_start":169,"byte_end":188,"line_start":4,"line_end":4,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use crate::scene::Scene;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\game_framework.rs","byte_start":165,"byte_end":191,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::scene::Scene;","highlight_start":1,"highlight_end":25},{"text":"use crate::input::{InputManager, Key};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::scene::Scene`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_framework.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::scene::Scene;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `InputManager` and `Key`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_framework.rs","byte_start":210,"byte_end":222,"line_start":5,"line_end":5,"column_start":20,"column_end":32,"is_primary":true,"text":[{"text":"use crate::input::{InputManager, Key};","highlight_start":20,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\game_framework.rs","byte_start":224,"byte_end":227,"line_start":5,"line_end":5,"column_start":34,"column_end":37,"is_primary":true,"text":[{"text":"use crate::input::{InputManager, Key};","highlight_start":34,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\game_framework.rs","byte_start":191,"byte_end":231,"line_start":5,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::input::{InputManager, Key};","highlight_start":1,"highlight_end":39},{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `InputManager` and `Key`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_framework.rs:5:20\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::input::{InputManager, Key};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Isometry3`, `Perspective3`, `Point3`, `Translation3`, `UnitQuaternion`, and `Vector3`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\advanced_rendering.rs","byte_start":110,"byte_end":116,"line_start":3,"line_end":3,"column_start":24,"column_end":30,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":24,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\advanced_rendering.rs","byte_start":118,"byte_end":125,"line_start":3,"line_end":3,"column_start":32,"column_end":39,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":32,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\advanced_rendering.rs","byte_start":136,"byte_end":148,"line_start":3,"line_end":3,"column_start":50,"column_end":62,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":50,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\advanced_rendering.rs","byte_start":150,"byte_end":159,"line_start":3,"line_end":3,"column_start":64,"column_end":73,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":64,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\advanced_rendering.rs","byte_start":161,"byte_end":173,"line_start":3,"line_end":3,"column_start":75,"column_end":87,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":75,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\advanced_rendering.rs","byte_start":175,"byte_end":189,"line_start":3,"line_end":3,"column_start":89,"column_end":103,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":89,"highlight_end":103}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\advanced_rendering.rs","byte_start":110,"byte_end":127,"line_start":3,"line_end":3,"column_start":24,"column_end":41,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":24,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\advanced_rendering.rs","byte_start":134,"byte_end":189,"line_start":3,"line_end":3,"column_start":48,"column_end":103,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":48,"highlight_end":103}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\advanced_rendering.rs","byte_start":109,"byte_end":110,"line_start":3,"line_end":3,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\advanced_rendering.rs","byte_start":189,"byte_end":190,"line_start":3,"line_end":3,"column_start":103,"column_end":104,"is_primary":true,"text":[{"text":"use kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};","highlight_start":103,"highlight_end":104}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Isometry3`, `Perspective3`, `Point3`, `Translation3`, `UnitQuaternion`, and `Vector3`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\advanced_rendering.rs:3:24\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kiss3d::nalgebra::{Point3, Vector3, Matrix4, Perspective3, Isometry3, Translation3, UnitQuaternion};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `kiss3d::scene::SceneNode`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\advanced_rendering.rs","byte_start":197,"byte_end":221,"line_start":4,"line_end":4,"column_start":5,"column_end":29,"is_primary":true,"text":[{"text":"use kiss3d::scene::SceneNode;","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\advanced_rendering.rs","byte_start":193,"byte_end":224,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use kiss3d::scene::SceneNode;","highlight_start":1,"highlight_end":30},{"text":"use kiss3d::window::Window;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `kiss3d::scene::SceneNode`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\advanced_rendering.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kiss3d::scene::SceneNode;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `kiss3d::light::Light`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\advanced_rendering.rs","byte_start":257,"byte_end":277,"line_start":6,"line_end":6,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"use kiss3d::light::Light;","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\advanced_rendering.rs","byte_start":253,"byte_end":280,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use kiss3d::light::Light;","highlight_start":1,"highlight_end":26},{"text":"use kiss3d::camera::{Camera, ArcBall, FirstPerson};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `kiss3d::light::Light`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\advanced_rendering.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kiss3d::light::Light;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ArcBall`, `Camera`, and `FirstPerson`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\advanced_rendering.rs","byte_start":301,"byte_end":307,"line_start":7,"line_end":7,"column_start":22,"column_end":28,"is_primary":true,"text":[{"text":"use kiss3d::camera::{Camera, ArcBall, FirstPerson};","highlight_start":22,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\advanced_rendering.rs","byte_start":309,"byte_end":316,"line_start":7,"line_end":7,"column_start":30,"column_end":37,"is_primary":true,"text":[{"text":"use kiss3d::camera::{Camera, ArcBall, FirstPerson};","highlight_start":30,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\advanced_rendering.rs","byte_start":318,"byte_end":329,"line_start":7,"line_end":7,"column_start":39,"column_end":50,"is_primary":true,"text":[{"text":"use kiss3d::camera::{Camera, ArcBall, FirstPerson};","highlight_start":39,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\advanced_rendering.rs","byte_start":280,"byte_end":333,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use kiss3d::camera::{Camera, ArcBall, FirstPerson};","highlight_start":1,"highlight_end":52},{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `ArcBall`, `Camera`, and `FirstPerson`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\advanced_rendering.rs:7:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kiss3d::camera::{Camera, ArcBall, FirstPerson};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Collectible`, `Enemy`, `GameObject`, `Platform`, and `Player`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_templates.rs","byte_start":141,"byte_end":151,"line_start":4,"line_end":4,"column_start":27,"column_end":37,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};","highlight_start":27,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\game_templates.rs","byte_start":153,"byte_end":159,"line_start":4,"line_end":4,"column_start":39,"column_end":45,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};","highlight_start":39,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\game_templates.rs","byte_start":161,"byte_end":166,"line_start":4,"line_end":4,"column_start":47,"column_end":52,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};","highlight_start":47,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\game_templates.rs","byte_start":168,"byte_end":179,"line_start":4,"line_end":4,"column_start":54,"column_end":65,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};","highlight_start":54,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\game_templates.rs","byte_start":181,"byte_end":189,"line_start":4,"line_end":4,"column_start":67,"column_end":75,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};","highlight_start":67,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\game_templates.rs","byte_start":115,"byte_end":193,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};","highlight_start":1,"highlight_end":77},{"text":"use crate::scene::Scene;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Collectible`, `Enemy`, `GameObject`, `Platform`, and `Player`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_templates.rs:4:27\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::game_objects::{GameObject, Player, Enemy, Collectible, Platform};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::advanced_rendering::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_templates.rs","byte_start":263,"byte_end":291,"line_start":7,"line_end":7,"column_start":5,"column_end":33,"is_primary":true,"text":[{"text":"use crate::advanced_rendering::*;","highlight_start":5,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\game_templates.rs","byte_start":259,"byte_end":294,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::advanced_rendering::*;","highlight_start":1,"highlight_end":34},{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::advanced_rendering::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_templates.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::advanced_rendering::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `GameObject`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\physics.rs","byte_start":108,"byte_end":118,"line_start":4,"line_end":4,"column_start":27,"column_end":37,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, BoundingBox};","highlight_start":27,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `GameObject`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\physics.rs:4:27\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::game_objects::{GameObject, BoundingBox};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `GameObject`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_framework.rs","byte_start":113,"byte_end":123,"line_start":3,"line_end":3,"column_start":27,"column_end":37,"is_primary":true,"text":[{"text":"use crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};","highlight_start":27,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `GameObject`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_framework.rs:3:27\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::game_objects::{GameObject, Player, Collectible, Enemy, Platform};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `delta_time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_objects.rs","byte_start":3729,"byte_end":3739,"line_start":132,"line_end":132,"column_start":51,"column_end":61,"is_primary":true,"text":[{"text":"    pub fn apply_gravity(&mut self, gravity: f32, delta_time: f32) {","highlight_start":51,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\game_objects.rs","byte_start":3729,"byte_end":3739,"line_start":132,"line_end":132,"column_start":51,"column_end":61,"is_primary":true,"text":[{"text":"    pub fn apply_gravity(&mut self, gravity: f32, delta_time: f32) {","highlight_start":51,"highlight_end":61}],"label":null,"suggested_replacement":"_delta_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `delta_time`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_objects.rs:132:51\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn apply_gravity(&mut self, gravity: f32, delta_time: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_delta_time`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `current_time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_objects.rs","byte_start":13861,"byte_end":13873,"line_start":481,"line_end":481,"column_start":60,"column_end":72,"is_primary":true,"text":[{"text":"    pub fn update_ai(&mut self, player_position: Vector3D, current_time: f32) {","highlight_start":60,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\game_objects.rs","byte_start":13861,"byte_end":13873,"line_start":481,"line_end":481,"column_start":60,"column_end":72,"is_primary":true,"text":[{"text":"    pub fn update_ai(&mut self, player_position: Vector3D, current_time: f32) {","highlight_start":60,"highlight_end":72}],"label":null,"suggested_replacement":"_current_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `current_time`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_objects.rs:481:60\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m481\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn update_ai(&mut self, player_position: Vector3D, current_time: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_current_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `scene`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\rendering.rs","byte_start":2017,"byte_end":2022,"line_start":66,"line_end":66,"column_start":30,"column_end":35,"is_primary":true,"text":[{"text":"    pub fn render(&mut self, scene: &Scene) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":30,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\rendering.rs","byte_start":2017,"byte_end":2022,"line_start":66,"line_end":66,"column_start":30,"column_end":35,"is_primary":true,"text":[{"text":"    pub fn render(&mut self, scene: &Scene) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":30,"highlight_end":35}],"label":null,"suggested_replacement":"_scene","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `scene`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\rendering.rs:66:30\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn render(&mut self, scene: &Scene) -> Result<(), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_scene`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `title`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\rendering.rs","byte_start":4060,"byte_end":4065,"line_start":131,"line_end":131,"column_start":40,"column_end":45,"is_primary":true,"text":[{"text":"    pub fn set_window_title(&mut self, title: &str) {","highlight_start":40,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\rendering.rs","byte_start":4060,"byte_end":4065,"line_start":131,"line_end":131,"column_start":40,"column_end":45,"is_primary":true,"text":[{"text":"    pub fn set_window_title(&mut self, title: &str) {","highlight_start":40,"highlight_end":45}],"label":null,"suggested_replacement":"_title","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `title`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\rendering.rs:131:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set_window_title(&mut self, title: &str) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_title`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `camera`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\rendering.rs","byte_start":4883,"byte_end":4889,"line_start":148,"line_end":148,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.arcball_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\rendering.rs","byte_start":4883,"byte_end":4889,"line_start":148,"line_end":148,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.arcball_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":"_camera","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `camera`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\rendering.rs:148:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m148\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if let Some(ref mut camera) = self.arcball_camera {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_camera`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `camera`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\rendering.rs","byte_start":5087,"byte_end":5093,"line_start":153,"line_end":153,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.fps_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\rendering.rs","byte_start":5087,"byte_end":5093,"line_start":153,"line_end":153,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.fps_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":"_camera","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `camera`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\rendering.rs:153:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m153\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if let Some(ref mut camera) = self.fps_camera {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_camera`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `kiss3d_direction`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\rendering.rs","byte_start":5140,"byte_end":5156,"line_start":154,"line_end":154,"column_start":25,"column_end":41,"is_primary":true,"text":[{"text":"                    let kiss3d_direction = Vector3::new(direction.x, direction.y, direction.z);","highlight_start":25,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\rendering.rs","byte_start":5140,"byte_end":5156,"line_start":154,"line_end":154,"column_start":25,"column_end":41,"is_primary":true,"text":[{"text":"                    let kiss3d_direction = Vector3::new(direction.x, direction.y, direction.z);","highlight_start":25,"highlight_end":41}],"label":null,"suggested_replacement":"_kiss3d_direction","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `kiss3d_direction`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\rendering.rs:154:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m154\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let kiss3d_direction = Vector3::new(direction.x, direction.y, direction.z);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_kiss3d_direction`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `amount`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\rendering.rs","byte_start":4692,"byte_end":4698,"line_start":144,"line_end":144,"column_start":56,"column_end":62,"is_primary":true,"text":[{"text":"    pub fn move_camera(&mut self, direction: Vector3D, amount: f32) {","highlight_start":56,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\rendering.rs","byte_start":4692,"byte_end":4698,"line_start":144,"line_end":144,"column_start":56,"column_end":62,"is_primary":true,"text":[{"text":"    pub fn move_camera(&mut self, direction: Vector3D, amount: f32) {","highlight_start":56,"highlight_end":62}],"label":null,"suggested_replacement":"_amount","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `amount`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\rendering.rs:144:56\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m144\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn move_camera(&mut self, direction: Vector3D, amount: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_amount`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `camera`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\rendering.rs","byte_start":5635,"byte_end":5641,"line_start":167,"line_end":167,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.fps_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\rendering.rs","byte_start":5635,"byte_end":5641,"line_start":167,"line_end":167,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.fps_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":"_camera","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `camera`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\rendering.rs:167:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m167\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if let Some(ref mut camera) = self.fps_camera {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_camera`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `yaw`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\rendering.rs","byte_start":5367,"byte_end":5370,"line_start":161,"line_end":161,"column_start":37,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn rotate_camera(&mut self, yaw: f32, pitch: f32) {","highlight_start":37,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\rendering.rs","byte_start":5367,"byte_end":5370,"line_start":161,"line_end":161,"column_start":37,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn rotate_camera(&mut self, yaw: f32, pitch: f32) {","highlight_start":37,"highlight_end":40}],"label":null,"suggested_replacement":"_yaw","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `yaw`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\rendering.rs:161:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn rotate_camera(&mut self, yaw: f32, pitch: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_yaw`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `pitch`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\rendering.rs","byte_start":5377,"byte_end":5382,"line_start":161,"line_end":161,"column_start":47,"column_end":52,"is_primary":true,"text":[{"text":"    pub fn rotate_camera(&mut self, yaw: f32, pitch: f32) {","highlight_start":47,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\rendering.rs","byte_start":5377,"byte_end":5382,"line_start":161,"line_end":161,"column_start":47,"column_end":52,"is_primary":true,"text":[{"text":"    pub fn rotate_camera(&mut self, yaw: f32, pitch: f32) {","highlight_start":47,"highlight_end":52}],"label":null,"suggested_replacement":"_pitch","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `pitch`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\rendering.rs:161:47\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn rotate_camera(&mut self, yaw: f32, pitch: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_pitch`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `camera`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\rendering.rs","byte_start":5940,"byte_end":5946,"line_start":177,"line_end":177,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.arcball_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\rendering.rs","byte_start":5940,"byte_end":5946,"line_start":177,"line_end":177,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"                if let Some(ref mut camera) = self.arcball_camera {","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":"_camera","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `camera`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\rendering.rs:177:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if let Some(ref mut camera) = self.arcball_camera {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_camera`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `amount`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\rendering.rs","byte_start":5816,"byte_end":5822,"line_start":174,"line_end":174,"column_start":35,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn zoom_camera(&mut self, amount: f32) {","highlight_start":35,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\rendering.rs","byte_start":5816,"byte_end":5822,"line_start":174,"line_end":174,"column_start":35,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn zoom_camera(&mut self, amount: f32) {","highlight_start":35,"highlight_end":41}],"label":null,"suggested_replacement":"_amount","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `amount`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\rendering.rs:174:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m174\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn zoom_camera(&mut self, amount: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_amount`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unreachable pattern","code":{"code":"unreachable_patterns","explanation":null},"level":"warning","spans":[{"file_name":"src\\input.rs","byte_start":6376,"byte_end":6377,"line_start":168,"line_end":168,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"                        _ => {},","highlight_start":25,"highlight_end":26}],"label":"no value can reach this","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"multiple earlier patterns match some of the same values","code":null,"level":"note","spans":[{"file_name":"src\\input.rs","byte_start":4652,"byte_end":4680,"line_start":140,"line_end":140,"column_start":25,"column_end":53,"is_primary":false,"text":[{"text":"                        kiss3d::event::Action::Press => {","highlight_start":25,"highlight_end":53}],"label":"matches some of the same values","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\input.rs","byte_start":5511,"byte_end":5541,"line_start":154,"line_end":154,"column_start":25,"column_end":55,"is_primary":false,"text":[{"text":"                        kiss3d::event::Action::Release => {","highlight_start":25,"highlight_end":55}],"label":"matches some of the same values","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\input.rs","byte_start":6376,"byte_end":6377,"line_start":168,"line_end":168,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"                        _ => {},","highlight_start":25,"highlight_end":26}],"label":"collectively making this unreachable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"`#[warn(unreachable_patterns)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unreachable pattern\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\input.rs:168:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        _ => {},\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mno value can reach this\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: multiple earlier patterns match some of the same values\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\input.rs:168:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m140\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        kiss3d::event::Action::Press => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmatches some of the same values\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m154\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        kiss3d::event::Action::Release => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmatches some of the same values\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        _ => {},\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mcollectively making this unreachable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unreachable_patterns)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `dt`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\physics.rs","byte_start":2542,"byte_end":2544,"line_start":93,"line_end":93,"column_start":32,"column_end":34,"is_primary":true,"text":[{"text":"    fn apply_forces(&mut self, dt: f32) {","highlight_start":32,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\physics.rs","byte_start":2542,"byte_end":2544,"line_start":93,"line_end":93,"column_start":32,"column_end":34,"is_primary":true,"text":[{"text":"    fn apply_forces(&mut self, dt: f32) {","highlight_start":32,"highlight_end":34}],"label":null,"suggested_replacement":"_dt","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `dt`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\physics.rs:93:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m93\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn apply_forces(&mut self, dt: f32) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_dt`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `body1`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\physics.rs","byte_start":6815,"byte_end":6820,"line_start":205,"line_end":205,"column_start":68,"column_end":73,"is_primary":true,"text":[{"text":"    fn handle_trigger_collision(&mut self, id1: usize, id2: usize, body1: &RigidBody, body2: &RigidBody) {","highlight_start":68,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\physics.rs","byte_start":6815,"byte_end":6820,"line_start":205,"line_end":205,"column_start":68,"column_end":73,"is_primary":true,"text":[{"text":"    fn handle_trigger_collision(&mut self, id1: usize, id2: usize, body1: &RigidBody, body2: &RigidBody) {","highlight_start":68,"highlight_end":73}],"label":null,"suggested_replacement":"_body1","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `body1`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\physics.rs:205:68\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn handle_trigger_collision(&mut self, id1: usize, id2: usize, body1: &RigidBody, body2: &RigidBody) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_body1`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `body2`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\physics.rs","byte_start":6834,"byte_end":6839,"line_start":205,"line_end":205,"column_start":87,"column_end":92,"is_primary":true,"text":[{"text":"    fn handle_trigger_collision(&mut self, id1: usize, id2: usize, body1: &RigidBody, body2: &RigidBody) {","highlight_start":87,"highlight_end":92}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\physics.rs","byte_start":6834,"byte_end":6839,"line_start":205,"line_end":205,"column_start":87,"column_end":92,"is_primary":true,"text":[{"text":"    fn handle_trigger_collision(&mut self, id1: usize, id2: usize, body1: &RigidBody, body2: &RigidBody) {","highlight_start":87,"highlight_end":92}],"label":null,"suggested_replacement":"_body2","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `body2`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\physics.rs:205:87\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn handle_trigger_collision(&mut self, id1: usize, id2: usize, body1: &RigidBody, body2: &RigidBody) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_body2`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id1`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\physics.rs","byte_start":18484,"byte_end":18487,"line_start":549,"line_end":549,"column_start":13,"column_end":16,"is_primary":true,"text":[{"text":"        let id1 = world.add_rigid_body(body1);","highlight_start":13,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\physics.rs","byte_start":18484,"byte_end":18487,"line_start":549,"line_end":549,"column_start":13,"column_end":16,"is_primary":true,"text":[{"text":"        let id1 = world.add_rigid_body(body1);","highlight_start":13,"highlight_end":16}],"label":null,"suggested_replacement":"_id1","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `id1`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\physics.rs:549:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m549\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let id1 = world.add_rigid_body(body1);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_id1`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id2`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\physics.rs","byte_start":18532,"byte_end":18535,"line_start":550,"line_end":550,"column_start":13,"column_end":16,"is_primary":true,"text":[{"text":"        let id2 = world.add_rigid_body(body2);","highlight_start":13,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\physics.rs","byte_start":18532,"byte_end":18535,"line_start":550,"line_end":550,"column_start":13,"column_end":16,"is_primary":true,"text":[{"text":"        let id2 = world.add_rigid_body(body2);","highlight_start":13,"highlight_end":16}],"label":null,"suggested_replacement":"_id2","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `id2`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\physics.rs:550:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m550\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let id2 = world.add_rigid_body(body2);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_id2`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `current`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\audio.rs","byte_start":4018,"byte_end":4025,"line_start":124,"line_end":124,"column_start":21,"column_end":28,"is_primary":true,"text":[{"text":"        if let Some(current) = &self.current_music {","highlight_start":21,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\audio.rs","byte_start":4018,"byte_end":4025,"line_start":124,"line_end":124,"column_start":21,"column_end":28,"is_primary":true,"text":[{"text":"        if let Some(current) = &self.current_music {","highlight_start":21,"highlight_end":28}],"label":null,"suggested_replacement":"_current","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `current`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\audio.rs:124:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m124\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(current) = &self.current_music {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_current`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `delta_time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_framework.rs","byte_start":12766,"byte_end":12776,"line_start":455,"line_end":455,"column_start":30,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn update(&mut self, delta_time: f32) {","highlight_start":30,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\game_framework.rs","byte_start":12766,"byte_end":12776,"line_start":455,"line_end":455,"column_start":30,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn update(&mut self, delta_time: f32) {","highlight_start":30,"highlight_end":40}],"label":null,"suggested_replacement":"_delta_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `delta_time`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_framework.rs:455:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m455\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn update(&mut self, delta_time: f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_delta_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `player_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_templates.rs","byte_start":14725,"byte_end":14734,"line_start":512,"line_end":512,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"        let player_id = scene.add_player(self.config.player_start_position);","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\game_templates.rs","byte_start":14725,"byte_end":14734,"line_start":512,"line_end":512,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"        let player_id = scene.add_player(self.config.player_start_position);","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":"_player_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `player_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_templates.rs:512:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m512\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let player_id = scene.add_player(self.config.player_start_position);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_player_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `delta_time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_templates.rs","byte_start":15849,"byte_end":15859,"line_start":542,"line_end":542,"column_start":26,"column_end":36,"is_primary":true,"text":[{"text":"    fn update(&mut self, delta_time: f32, input: &InputManager, framework: &mut GameFramework) -> GameUpdateResult {","highlight_start":26,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\game_templates.rs","byte_start":15849,"byte_end":15859,"line_start":542,"line_end":542,"column_start":26,"column_end":36,"is_primary":true,"text":[{"text":"    fn update(&mut self, delta_time: f32, input: &InputManager, framework: &mut GameFramework) -> GameUpdateResult {","highlight_start":26,"highlight_end":36}],"label":null,"suggested_replacement":"_delta_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `delta_time`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_templates.rs:542:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m542\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn update(&mut self, delta_time: f32, input: &InputManager, framework: &mut GameFramework) -> GameUpdateResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_delta_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `framework`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_templates.rs","byte_start":15888,"byte_end":15897,"line_start":542,"line_end":542,"column_start":65,"column_end":74,"is_primary":true,"text":[{"text":"    fn update(&mut self, delta_time: f32, input: &InputManager, framework: &mut GameFramework) -> GameUpdateResult {","highlight_start":65,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\game_templates.rs","byte_start":15888,"byte_end":15897,"line_start":542,"line_end":542,"column_start":65,"column_end":74,"is_primary":true,"text":[{"text":"    fn update(&mut self, delta_time: f32, input: &InputManager, framework: &mut GameFramework) -> GameUpdateResult {","highlight_start":65,"highlight_end":74}],"label":null,"suggested_replacement":"_framework","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `framework`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_templates.rs:542:65\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m542\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn update(&mut self, delta_time: f32, input: &InputManager, framework: &mut GameFramework) -> GameUpdateResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_framework`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `size`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_templates.rs","byte_start":32983,"byte_end":32987,"line_start":1018,"line_end":1018,"column_start":24,"column_end":28,"is_primary":true,"text":[{"text":"        for (position, size) in platforms {","highlight_start":24,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\game_templates.rs","byte_start":32983,"byte_end":32987,"line_start":1018,"line_end":1018,"column_start":24,"column_end":28,"is_primary":true,"text":[{"text":"        for (position, size) in platforms {","highlight_start":24,"highlight_end":28}],"label":null,"suggested_replacement":"_size","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `size`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_templates.rs:1018:24\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1018\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        for (position, size) in platforms {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_size`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `config` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1367,"byte_end":1377,"line_start":55,"line_end":55,"column_start":12,"column_end":22,"is_primary":false,"text":[{"text":"pub struct GameEngine {","highlight_start":12,"highlight_end":22}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":1385,"byte_end":1391,"line_start":56,"line_end":56,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: EngineConfig,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `config` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:56:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m55\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct GameEngine {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m56\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: EngineConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `max_health` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_objects.rs","byte_start":11228,"byte_end":11233,"line_start":380,"line_end":380,"column_start":12,"column_end":17,"is_primary":false,"text":[{"text":"pub struct Enemy {","highlight_start":12,"highlight_end":17}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\game_objects.rs","byte_start":11401,"byte_end":11411,"line_start":388,"line_end":388,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    max_health: i32,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `max_health` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_objects.rs:388:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m380\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct Enemy {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m388\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    max_health: i32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `create_particle`, `interpolate_color`, and `interpolate_size` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\advanced_rendering.rs","byte_start":9289,"byte_end":9310,"line_start":381,"line_end":381,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"impl AdvancedRenderer {","highlight_start":1,"highlight_end":22}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\advanced_rendering.rs","byte_start":22481,"byte_end":22496,"line_start":738,"line_end":738,"column_start":8,"column_end":23,"is_primary":true,"text":[{"text":"    fn create_particle(&self, emitter: &ParticleEmitter) -> Particle {","highlight_start":8,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\advanced_rendering.rs","byte_start":25359,"byte_end":25376,"line_start":805,"line_end":805,"column_start":8,"column_end":25,"is_primary":true,"text":[{"text":"    fn interpolate_color(&self, color_curve: &[(f32, (f32, f32, f32, f32))], t: f32) -> (f32, f32, f32, f32) {","highlight_start":8,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\advanced_rendering.rs","byte_start":25523,"byte_end":25539,"line_start":809,"line_end":809,"column_start":8,"column_end":24,"is_primary":true,"text":[{"text":"    fn interpolate_size(&self, size_curve: &[(f32, f32)], t: f32) -> f32 {","highlight_start":8,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `create_particle`, `interpolate_color`, and `interpolate_size` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\advanced_rendering.rs:738:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m381\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl AdvancedRenderer {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m738\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn create_particle(&self, emitter: &ParticleEmitter) -> Particle {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m805\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn interpolate_color(&self, color_curve: &[(f32, (f32, f32, f32, f32))], t: f32) -> (f32, f32, f32, f32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m809\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn interpolate_size(&self, size_curve: &[(f32, f32)], t: f32) -> f32 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `level_system` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\game_templates.rs","byte_start":4689,"byte_end":4696,"line_start":185,"line_end":185,"column_start":12,"column_end":19,"is_primary":false,"text":[{"text":"pub struct RPGGame {","highlight_start":12,"highlight_end":19}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\game_templates.rs","byte_start":4910,"byte_end":4922,"line_start":193,"line_end":193,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    level_system: LevelSystem,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `level_system` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\game_templates.rs:193:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct RPGGame {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m193\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    level_system: LevelSystem,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"40 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 40 warnings emitted\u001b[0m\n\n"}
