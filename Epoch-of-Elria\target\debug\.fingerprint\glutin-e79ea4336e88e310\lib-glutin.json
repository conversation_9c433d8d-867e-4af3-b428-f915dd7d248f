{"rustc": 16591470773350601817, "features": "[\"default\", \"glutin_glx_sys\", \"wayland\", \"wayland-client\", \"wayland-egl\", \"x11\"]", "declared_features": "[\"default\", \"glutin_glx_sys\", \"serde\", \"wayland\", \"wayland-client\", \"wayland-egl\", \"x11\"]", "target": 8798576871209898272, "profile": 2241668132362809309, "path": 10122675766720153317, "deps": [[1365408723356066744, "winit", false, 8816437821788397550], [2823906784491351928, "glutin_wgl_sys", false, 13287281590515986314], [10020888071089587331, "<PERSON>ap<PERSON>", false, 11911911858306910069], [11641406201058336332, "parking_lot", false, 17704089081895887541], [11723284583626592924, "libloading", false, 4718875242820439672], [14153367844739996026, "glutin_egl_sys", false, 7418483158701754887], [17917672826516349275, "lazy_static", false, 9776750328437871497]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\glutin-e79ea4336e88e310\\dep-lib-glutin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}